2025-07-07T01:11:38.561720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:11:38.562426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:11:38.607643Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:14:43.148374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:14:43.148598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:14:43.243830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:14:43.243921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:14:43.289695Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:15:12.346019Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:15:12.346073Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:15:12.953388Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:15:12.953479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:15:12.953563Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:15:13.346871Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:15:13.346905Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:15:13.372464Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:15:16.055337Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:15:16.055373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:15:16.055501Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:16:54.618788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:16:54.618966Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:16:54.647555Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:16:58.809530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:16:58.809563Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:16:59.010613Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:16:59.010636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:16:59.010683Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:17:00.517695Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:17:00.517728Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:17:00.517797Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:22:51.282094Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:22:51.282907Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:22:51.323635Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:23:00.367196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:23:00.367230Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:23:00.781511Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:23:00.781562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:23:00.781648Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:23:02.470510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:23:02.470539Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:23:02.479444Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:32:09.666622Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:32:09.667496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:32:09.751372Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:33:11.049511Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\card.ts"), AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:11.049540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:11.161328Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components")}
2025-07-07T01:33:11.161359Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:11.161409Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:33:25.755012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components\\ui\\input.ts")}
2025-07-07T01:33:25.755057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:36.659857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\label.ts")}
2025-07-07T01:33:36.659903Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:36.754892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:36.754927Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:36.755019Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:33:41.149365Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\button.ts"), AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:41.149392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:45.854840Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\alert.ts"), AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:45.854866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:50.263720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components\\ui\\checkbox.ts")}
2025-07-07T01:33:50.263810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:54.759710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\select.ts")}
2025-07-07T01:33:54.759746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:54.856530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:54.856668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:54.856910Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:33:59.461186Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\shiny-button.ts")}
2025-07-07T01:33:59.461213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:59.557477Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:33:59.557528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:33:59.557660Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:34:03.956074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components\\ui\\shimmer-button.ts")}
2025-07-07T01:34:03.956156Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:34:09.058305Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components\\ui\\pulsating-button.ts")}
2025-07-07T01:34:09.058424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:34:13.759046Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui"), AnchoredSystemPathBuf("apps\\web\\components\\ui\\rainbow-button.ts")}
2025-07-07T01:34:13.759074Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:34:18.753211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\animated-beam.ts"), AnchoredSystemPathBuf("apps\\web\\components\\ui")}
2025-07-07T01:34:18.753228Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:34:27.596946Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:34:27.597002Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:34:27.651868Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:35:49.513728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\input.tsx")}
2025-07-07T01:35:49.513877Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:36:03.796570Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\input.tsx")}
2025-07-07T01:36:03.796607Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:36:18.807371Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\select.tsx")}
2025-07-07T01:36:18.807408Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:36:31.803077Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\select.tsx")}
2025-07-07T01:36:31.803115Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:36:55.398206Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:36:55.398236Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:37:14.300211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:37:14.300242Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:37:23.491616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:37:23.491693Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:37:23.556292Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:37:43.132546Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\auth"), AnchoredSystemPathBuf("packages\\auth\\.turbo")}
2025-07-07T01:37:43.132629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/auth"), path: AnchoredSystemPathBuf("packages\\auth") }}))
2025-07-07T01:37:43.139895Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:38:22.231218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:22.231267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:22.534248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:22.534287Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:22.534375Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:38:24.225233Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:24.225249Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:24.225281Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:38:47.435287Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:47.435323Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:47.497833Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:38:47.826817Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:47.826853Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:47.841768Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:38:49.425513Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:38:49.425550Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:38:49.434241Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:39:22.234861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:39:22.234982Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:39:22.269186Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:39:22.331676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:39:22.331717Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:39:22.331843Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:39:22.630290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:39:22.630321Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:39:22.630403Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:39:24.131616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:39:24.131641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:39:24.142977Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:41:03.725614Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:41:03.725674Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:41:03.767577Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:41:10.219806Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:41:10.219893Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:41:10.312909Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:41:10.312942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:41:10.313039Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:41:10.718911Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:41:10.719006Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:41:10.719096Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:41:12.522641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:41:12.522693Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:41:12.522805Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:42:37.520382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:42:37.520431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:44:26.116933Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:44:26.117127Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:44:36.026619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:44:36.026646Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:45:42.420475Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\shimmer-button.tsx")}
2025-07-07T01:45:42.420510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:46:09.312329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\pulsating-button.tsx")}
2025-07-07T01:46:09.312410Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:46:22.126598Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\interactive-hover-button.tsx")}
2025-07-07T01:46:22.126627Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:46:22.220859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui")}
2025-07-07T01:46:22.220880Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:46:22.220918Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:46:29.313540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\button.ts")}
2025-07-07T01:46:29.313569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:46:29.422434Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\components\\ui\\button.ts")}
2025-07-07T01:46:29.422516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:46:29.422593Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:46:50.925953Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\index.ts")}
2025-07-07T01:46:50.925990Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:47:05.149443Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:47:05.149500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:47:05.153790Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:48:53.343138Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:48:53.343234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:48:53.394910Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:49:03.138505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:49:03.138543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:49:03.242481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:49:03.242546Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:49:03.242631Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:49:04.142463Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:49:04.142497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:49:04.142626Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:49:10.137528Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:49:10.137570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:49:10.165576Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:49:58.442605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-07-07T01:49:58.442673Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:50:02.839325Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-07-07T01:50:02.839402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:50:03.335146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-07T01:50:03.335172Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-07T01:50:04.536276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package-lock.json")}
2025-07-07T01:50:04.536314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:50:27.746549Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\src\\components\\ui\\animated-beam.tsx")}
2025-07-07T01:50:27.746585Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T01:51:55.576871Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T01:51:55.577382Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:51:55.659687Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T01:52:04.674448Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:52:04.674484Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:52:05.175049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:52:05.175081Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:52:05.199484Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:52:07.181310Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T01:52:07.181335Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T01:52:07.181413Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T01:52:25.073409Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-07-07T01:52:25.073443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:52:25.872611Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules")}
2025-07-07T01:52:25.872647Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T01:52:26.473509Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-07T01:52:26.473541Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-07T01:52:27.182810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package-lock.json")}
2025-07-07T01:52:27.182838Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T02:42:33.718752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T02:42:33.720572Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T02:42:33.786081Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T02:42:33.812048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\ui\\.turbo"), AnchoredSystemPathBuf("apps\\web\\.turbo"), AnchoredSystemPathBuf("packages\\auth\\.turbo")}
2025-07-07T02:42:33.812086Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }, WorkspacePackage { name: Other("@chia/auth"), path: AnchoredSystemPathBuf("packages\\auth") }, WorkspacePackage { name: Other("@chia/ui"), path: AnchoredSystemPathBuf("packages\\ui") }}))
2025-07-07T02:42:38.216101Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:42:38.216145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:42:38.452267Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:42:38.452745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:42:38.452766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:42:38.452892Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:42:46.616411Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:42:46.616439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:42:46.652542Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:42:47.114228Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:42:47.114265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:42:47.114377Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:42:47.311240Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:42:47.311279Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:42:47.371083Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:43:12.913137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:43:12.913192Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:43:12.913290Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:43:13.918926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:43:13.918963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:43:13.970014Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:43:14.123595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:43:14.123627Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:43:14.137796Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:43:14.314953Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:43:14.315031Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:43:14.341459Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:43:46.612944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\page.tsx")}
2025-07-07T02:43:46.612979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:43:54.020223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\contacto\\page.tsx")}
2025-07-07T02:43:54.020252Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:46:11.312540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\chat\\page.tsx")}
2025-07-07T02:46:11.312584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:46:22.910997Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\acerca\\page.tsx")}
2025-07-07T02:46:22.911028Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:46:33.437884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\certificados\\page.tsx")}
2025-07-07T02:46:33.437944Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:46:48.109765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\pagos\\page.tsx")}
2025-07-07T02:46:48.109793Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:47:00.912299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\pagos\\page.tsx")}
2025-07-07T02:47:00.912322Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:48:32.016952Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\pagos\\page.tsx")}
2025-07-07T02:48:32.018113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:48:46.711304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\licencias\\page.tsx")}
2025-07-07T02:48:46.711403Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:48:59.010600Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\licencias\\page.tsx")}
2025-07-07T02:48:59.010712Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:49:12.112455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\licencias\\page.tsx")}
2025-07-07T02:49:12.112485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:49:12.216191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\licencias\\page.tsx")}
2025-07-07T02:49:12.216224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:49:12.216315Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:49:25.432187Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\registro\\page.tsx")}
2025-07-07T02:49:25.432250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:49:39.810845Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\app\\servicios\\registro\\page.tsx")}
2025-07-07T02:49:39.810866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:51:09.092554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T02:51:09.092680Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T02:51:09.142874Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T02:51:17.788788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:51:17.788820Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:51:18.378660Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:51:18.378696Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:51:18.506047Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:51:18.506434Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:51:18.506459Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:51:18.506547Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:52:29.787003Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:52:29.787039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
2025-07-07T02:52:29.787152Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-07T02:52:38.865984Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo")}
2025-07-07T02:52:38.866061Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-07T02:52:38.926755Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-07T02:52:41.374697Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\web\\.next")}
2025-07-07T02:52:41.374729Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@chia/web"), path: AnchoredSystemPathBuf("apps\\web") }}))
